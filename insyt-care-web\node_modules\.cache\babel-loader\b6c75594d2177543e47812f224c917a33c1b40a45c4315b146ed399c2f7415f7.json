{"ast": null, "code": "var _jsxFileName = \"D:\\\\Softwares\\\\insyt-care\\\\insyt-care-web\\\\src\\\\pages\\\\Login.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from \"react\";\nimport { Checkbox, FormControl, FormControlLabel, Grid, IconButton, Button, CircularProgress, Box, Typography } from \"@mui/material\";\nimport { Controller, useForm } from \"react-hook-form\";\nimport z from \"zod\";\nimport { zodResolver } from \"@hookform/resolvers/zod\";\nimport { VisibilityOff, Visibility, CheckBox, CheckBoxOutlineBlank } from \"@mui/icons-material\";\nimport { COLLECTIONS } from \"@constants/app\";\nimport { signInWithEmailAndPassword } from \"firebase/auth\";\nimport { app, auth, db, messaging } from \"config/firebase.config\";\nimport { doc, getDoc, updateDoc } from \"firebase/firestore\";\nimport { useNavigate } from \"react-router\";\nimport BG_IMAGE from \"@assets/AUTH_LEFTSIDE_BG.png\";\nimport SIGN_IN_LEFT_CARTOON from \"@assets/SIGN_IN_LEFT_CARTOON.webp\";\nimport LOGO from \"@assets/LOGO_BG_WHITE_ROUND.png\";\nimport styled from \"styled-components\";\nimport Field from \"@ui/Field\";\nimport { useSnackbar } from \"notistack\";\nimport { getMessaging, getToken, isSupported } from \"firebase/messaging\";\nimport { useEffect } from \"react\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nexport const loginSchema = z.object({\n  email: z.string().nonempty(\"Email is required\").email(\"Must be a valid email\"),\n  password: z.string().nonempty(\"Password is required\").min(6, \"Password must have at least 6 characters\"),\n  agreedToTC: z.boolean().refine(val => val === true, {\n    message: \"You must agree to the terms and conditions\"\n  })\n});\nconst StyledField = styled(Field)`\n  background-color: #f5f5f5;\n  width: 100%;\n  padding: 24px 16px;\n`;\n_c = StyledField;\nconst Login = () => {\n  _s();\n  var _errors$email, _errors$email2, _errors$password, _errors$password2;\n  document.title = \"Login - Insyt Care\";\n  const [showPassword, setShowPassword] = useState(false);\n  const navigate = useNavigate();\n  const {\n    enqueueSnackbar\n  } = useSnackbar();\n  const [fcm, setFCM] = useState(\"\");\n  const {\n    handleSubmit,\n    formState: {\n      errors,\n      isDirty,\n      isValid,\n      isSubmitting\n    },\n    control\n  } = useForm({\n    defaultValues: {\n      email: \"\",\n      password: \"\",\n      agreedToTC: false\n    },\n    mode: \"all\",\n    resolver: zodResolver(loginSchema)\n  });\n  async function submitFormToLogin(formValues) {\n    const {\n      agreedToTC,\n      ...rest\n    } = formValues;\n    await signInWithEmailAndPassword(auth, rest.email, rest.password).then(async res => {\n      const docSnapshot = await getDoc(doc(db, COLLECTIONS.USERS, res.user.uid));\n      const user = docSnapshot.data();\n\n      // Get the Firebase ID token for API authentication\n      const idToken = await res.user.getIdToken();\n\n      // HANDLE ADMIN\n      if (user.role === \"ADMIN\") {\n        navigate(\"/dashboard\");\n        localStorage.setItem(\"userId\", res.user.uid);\n        localStorage.setItem(\"access_token\", idToken);\n      }\n\n      // HANDLE NURSE\n      else if (user.role === \"NURSE\") {\n        localStorage.setItem(\"userId\", res.user.uid);\n        localStorage.setItem(\"access_token\", idToken);\n        await updateDoc(doc(db, COLLECTIONS.USERS, res.user.uid), {\n          fcmToken: fcm\n        });\n        navigate(\"/dashboard\");\n      }\n\n      // HANDLE CAREGIVER\n      else if (user.role === \"CAREGIVER\") {\n        localStorage.setItem(\"userId\", res.user.uid);\n        localStorage.setItem(\"access_token\", idToken);\n        await updateDoc(doc(db, COLLECTIONS.USERS, res.user.uid), {\n          fcmToken: fcm\n        });\n        navigate(\"/dashboard\");\n      }\n\n      // HANDLE CLIENT (if they can login)\n      else if (user.role === \"CLIENT\") {\n        localStorage.setItem(\"userId\", res.user.uid);\n        localStorage.setItem(\"access_token\", idToken);\n        // Clients might have different navigation logic\n        navigate(\"/dashboard\");\n      }\n    }).catch(error => {\n      console.error(\"SIGN IN > \", error);\n      // INTERNET NOT WORKING\n      if (error.code === \"auth/network-request-failed\") {\n        enqueueSnackbar(\"Make sure your internet is working\", {\n          variant: \"error\"\n        });\n      }\n      // USER NOT EXISTS\n      else if (error.code === \"auth/user-not-found\") {\n        enqueueSnackbar(\"Invalid email or password\", {\n          variant: \"error\"\n        });\n      }\n      // INVAVLID CREDENTIALS\n      else if (error.code === \"auth/wrong-password\" || \"auth/invalid-login-credentials\") {\n        enqueueSnackbar(\"Invalid email or password\", {\n          variant: \"error\"\n        });\n      } else {\n        enqueueSnackbar(\"Something went wrong\", {\n          variant: \"error\"\n        });\n      }\n    });\n  }\n  async function getPermission() {\n    if (await isSupported()) {\n      const permission = await Notification.requestPermission();\n      if (permission === \"granted\") {\n        const tokenFCM = await getToken(messaging, {\n          vapidKey: process.env.REACT_APP_FIREBASE_WEB_PUSH\n        });\n        console.log(\"fcm >>\", tokenFCM);\n        setFCM(tokenFCM);\n      } else if (permission === \"denied\") {\n        alert(\"You've denied the notifications.\");\n      }\n    }\n  }\n  useEffect(() => {\n    getPermission();\n  }, []);\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      justifyContent: \"center\",\n      alignItems: \"center\",\n      style: {\n        height: \"100vh\"\n      },\n      children: [/*#__PURE__*/_jsxDEV(Grid\n      // md={6}\n      // sm={12}\n      , {\n        size: {\n          xs: 12,\n          md: 6\n        },\n        sx: {\n          backgroundImage: `url(${BG_IMAGE})`,\n          backgroundRepeat: \"no-repeat\",\n          height: \"100vh\",\n          backgroundSize: \"cover\",\n          backgroundPosition: \"center\",\n          p: 4,\n          display: {\n            xs: \"none\",\n            sm: \"none\",\n            md: \"flex\"\n          },\n          flexDirection: \"column\",\n          justifyContent: \"space-between\"\n        },\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          display: \"flex\",\n          flexDirection: \"column\",\n          gap: 2,\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            display: \"flex\",\n            justifyContent: \"start\",\n            alignItems: \"center\",\n            gap: 1,\n            children: [/*#__PURE__*/_jsxDEV(\"img\", {\n              src: LOGO,\n              alt: \"insyt care logo\",\n              style: {\n                objectFit: \"contain\",\n                width: 42,\n                height: 42\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 168,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              textTransform: \"uppercase\",\n              color: \"#fff\",\n              fontWeight: 600,\n              variant: \"h6\",\n              children: \"Insytcare\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 169,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 166,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            textTransform: \"capitalize\",\n            color: \"#fff\",\n            fontWeight: 600,\n            variant: \"h5\",\n            children: \"Empowering Better Health Outcomes\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 173,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            textTransform: \"full-size-kana\",\n            color: \"#fff\",\n            fontWeight: 400,\n            variant: \"body1\",\n            children: \"We connect patients and healthcare professionals through a seamless, secure, and user-friendly platform that makes managing care simple and effective.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 176,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 165,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"img\", {\n          src: SIGN_IN_LEFT_CARTOON,\n          alt: \"illustration\",\n          style: {\n            objectFit: \"contain\",\n            maxHeight: 300\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 181,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 149,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid\n      // md={6} sm={12}\n      , {\n        size: {\n          xs: 12,\n          md: 6\n        },\n        sx: {\n          backgroundColor: \"white\",\n          padding: 4,\n          height: \"100vh\",\n          display: \"flex\",\n          justifyContent: \"center\",\n          alignItems: \"center\"\n        },\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            maxWidth: {\n              xs: \"100%\",\n              lg: \"70%\"\n            }\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            fontWeight: 500,\n            variant: \"h4\",\n            textAlign: \"center\",\n            display: \"block\",\n            mb: 3,\n            children: \"Sign In\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 196,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n            onSubmit: handleSubmit(submitFormToLogin),\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              display: \"flex\",\n              flexDirection: \"column\",\n              gap: 2,\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                position: \"relative\",\n                children: [/*#__PURE__*/_jsxDEV(Controller, {\n                  name: \"email\",\n                  control: control,\n                  render: ({\n                    field\n                  }) => {\n                    return /*#__PURE__*/_jsxDEV(StyledField, {\n                      placeholder: \"Email\",\n                      type: \"email\",\n                      ...field\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 206,\n                      columnNumber: 30\n                    }, this);\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 202,\n                  columnNumber: 19\n                }, this), (errors === null || errors === void 0 ? void 0 : (_errors$email = errors.email) === null || _errors$email === void 0 ? void 0 : _errors$email.message) && /*#__PURE__*/_jsxDEV(Typography, {\n                  color: \"error\",\n                  variant: \"caption\",\n                  children: errors === null || errors === void 0 ? void 0 : (_errors$email2 = errors.email) === null || _errors$email2 === void 0 ? void 0 : _errors$email2.message\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 210,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 201,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                position: \"relative\",\n                children: [/*#__PURE__*/_jsxDEV(Box, {\n                  position: \"relative\",\n                  children: [/*#__PURE__*/_jsxDEV(Controller, {\n                    name: \"password\",\n                    control: control,\n                    render: ({\n                      field\n                    }) => {\n                      return /*#__PURE__*/_jsxDEV(StyledField, {\n                        placeholder: \"Password\",\n                        type: showPassword ? \"text\" : \"password\",\n                        ...field\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 224,\n                        columnNumber: 27\n                      }, this);\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 219,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n                    onClick: () => setShowPassword(!showPassword),\n                    sx: {\n                      position: \"absolute\",\n                      top: 0,\n                      bottom: 0,\n                      right: 2\n                    },\n                    children: showPassword ? /*#__PURE__*/_jsxDEV(VisibilityOff, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 233,\n                      columnNumber: 39\n                    }, this) : /*#__PURE__*/_jsxDEV(Visibility, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 233,\n                      columnNumber: 59\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 229,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 218,\n                  columnNumber: 19\n                }, this), (errors === null || errors === void 0 ? void 0 : (_errors$password = errors.password) === null || _errors$password === void 0 ? void 0 : _errors$password.message) && /*#__PURE__*/_jsxDEV(Typography, {\n                  color: \"error\",\n                  variant: \"caption\",\n                  children: errors === null || errors === void 0 ? void 0 : (_errors$password2 = errors.password) === null || _errors$password2 === void 0 ? void 0 : _errors$password2.message\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 237,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 217,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 200,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(FormControl, {\n              sx: {\n                mt: 2\n              },\n              children: /*#__PURE__*/_jsxDEV(FormControlLabel, {\n                sx: {\n                  fontSize: 16,\n                  \".MuiTypography-root\": {\n                    fontSize: \"12px\"\n                  }\n                },\n                label: /*#__PURE__*/_jsxDEV(_Fragment, {\n                  children: \"By selecting Continue, you agree to our Terms of Service and acknowledge our Privacy Policy.\"\n                }, void 0, false),\n                control: /*#__PURE__*/_jsxDEV(Controller, {\n                  name: \"agreedToTC\",\n                  control: control,\n                  render: ({\n                    field\n                  }) => /*#__PURE__*/_jsxDEV(Checkbox, {\n                    ...field,\n                    color: \"primary\",\n                    checkedIcon: /*#__PURE__*/_jsxDEV(CheckBox, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 259,\n                      columnNumber: 40\n                    }, this),\n                    icon: /*#__PURE__*/_jsxDEV(CheckBoxOutlineBlank, {\n                      fill: \"#2D2D2D\",\n                      opacity: 0.5\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 260,\n                      columnNumber: 33\n                    }, this),\n                    sx: {\n                      mb: \"12px\"\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 256,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 252,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 246,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 245,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(FormControl, {\n              sx: {\n                display: \"block\",\n                marginTop: 2\n              },\n              children: /*#__PURE__*/_jsxDEV(Button, {\n                fullWidth: true,\n                variant: \"contained\",\n                color: \"primary\",\n                type: \"submit\",\n                sx: {\n                  fontSize: 16,\n                  borderRadius: 2,\n                  py: 1.2,\n                  textTransform: \"none\",\n                  fontWeight: 400\n                },\n                disabled: isSubmitting || !isDirty || !isValid || Object.keys(errors).length > 0,\n                children: [\"Sign In\", isSubmitting ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n                  size: 16,\n                  color: \"inherit\",\n                  sx: {\n                    marginLeft: 1\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 286,\n                  columnNumber: 35\n                }, this) : null]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 271,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 270,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 199,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 195,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 183,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 148,\n      columnNumber: 7\n    }, this)\n  }, void 0, false);\n};\n_s(Login, \"/BYTiGVQCWtCkMU+KLFi5RcQdl8=\", false, function () {\n  return [useNavigate, useSnackbar, useForm];\n});\n_c2 = Login;\nexport default Login;\nvar _c, _c2;\n$RefreshReg$(_c, \"StyledField\");\n$RefreshReg$(_c2, \"Login\");", "map": {"version": 3, "names": ["React", "useState", "Checkbox", "FormControl", "FormControlLabel", "Grid", "IconButton", "<PERSON><PERSON>", "CircularProgress", "Box", "Typography", "Controller", "useForm", "z", "zodResolver", "VisibilityOff", "Visibility", "CheckBox", "CheckBoxOutlineBlank", "COLLECTIONS", "signInWithEmailAndPassword", "app", "auth", "db", "messaging", "doc", "getDoc", "updateDoc", "useNavigate", "BG_IMAGE", "SIGN_IN_LEFT_CARTOON", "LOGO", "styled", "Field", "useSnackbar", "getMessaging", "getToken", "isSupported", "useEffect", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "loginSchema", "object", "email", "string", "nonempty", "password", "min", "agreedToTC", "boolean", "refine", "val", "message", "StyledField", "_c", "<PERSON><PERSON>", "_s", "_errors$email", "_errors$email2", "_errors$password", "_errors$password2", "document", "title", "showPassword", "setShowPassword", "navigate", "enqueueSnackbar", "fcm", "setFCM", "handleSubmit", "formState", "errors", "isDirty", "<PERSON><PERSON><PERSON><PERSON>", "isSubmitting", "control", "defaultValues", "mode", "resolver", "submitFormToLogin", "formValues", "rest", "then", "res", "docSnapshot", "USERS", "user", "uid", "data", "idToken", "getIdToken", "role", "localStorage", "setItem", "fcmToken", "catch", "error", "console", "code", "variant", "getPermission", "permission", "Notification", "requestPermission", "tokenFCM", "vapid<PERSON>ey", "process", "env", "REACT_APP_FIREBASE_WEB_PUSH", "log", "alert", "children", "container", "justifyContent", "alignItems", "style", "height", "size", "xs", "md", "sx", "backgroundImage", "backgroundRepeat", "backgroundSize", "backgroundPosition", "p", "display", "sm", "flexDirection", "gap", "src", "alt", "objectFit", "width", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "textTransform", "color", "fontWeight", "maxHeight", "backgroundColor", "padding", "max<PERSON><PERSON><PERSON>", "lg", "textAlign", "mb", "onSubmit", "position", "name", "render", "field", "placeholder", "type", "onClick", "top", "bottom", "right", "mt", "fontSize", "label", "checkedIcon", "icon", "fill", "opacity", "marginTop", "fullWidth", "borderRadius", "py", "disabled", "Object", "keys", "length", "marginLeft", "_c2", "$RefreshReg$"], "sources": ["D:/Softwares/insyt-care/insyt-care-web/src/pages/Login.jsx"], "sourcesContent": ["import React, { useState } from \"react\";\r\nimport {\r\n  Checkbox,\r\n  FormControl,\r\n  FormControlLabel,\r\n  Grid,\r\n  IconButton,\r\n  Button,\r\n  CircularProgress,\r\n  Box,\r\n  Typography,\r\n} from \"@mui/material\";\r\nimport { Controller, useForm } from \"react-hook-form\";\r\nimport z from \"zod\";\r\nimport { zodResolver } from \"@hookform/resolvers/zod\";\r\nimport { VisibilityOff, Visibility, CheckBox, CheckBoxOutlineBlank } from \"@mui/icons-material\";\r\nimport { COLLECTIONS } from \"@constants/app\";\r\nimport { signInWithEmailAndPassword } from \"firebase/auth\";\r\nimport { app, auth, db, messaging } from \"config/firebase.config\";\r\nimport { doc, getDoc, updateDoc } from \"firebase/firestore\";\r\nimport { useNavigate } from \"react-router\";\r\nimport BG_IMAGE from \"@assets/AUTH_LEFTSIDE_BG.png\";\r\nimport SIGN_IN_LEFT_CARTOON from \"@assets/SIGN_IN_LEFT_CARTOON.webp\";\r\nimport LOGO from \"@assets/LOGO_BG_WHITE_ROUND.png\";\r\nimport styled from \"styled-components\";\r\nimport Field from \"@ui/Field\";\r\nimport { useSnackbar } from \"notistack\";\r\nimport { getMessaging, getToken, isSupported } from \"firebase/messaging\";\r\nimport { useEffect } from \"react\";\r\n\r\nexport const loginSchema = z.object({\r\n  email: z.string().nonempty(\"Email is required\").email(\"Must be a valid email\"),\r\n  password: z.string().nonempty(\"Password is required\").min(6, \"Password must have at least 6 characters\"),\r\n  agreedToTC: z.boolean().refine((val) => val === true, {\r\n    message: \"You must agree to the terms and conditions\",\r\n  }),\r\n});\r\n\r\nconst StyledField = styled(Field)`\r\n  background-color: #f5f5f5;\r\n  width: 100%;\r\n  padding: 24px 16px;\r\n`;\r\n\r\nconst Login = () => {\r\n  document.title = \"Login - Insyt Care\";\r\n  const [showPassword, setShowPassword] = useState(false);\r\n  const navigate = useNavigate();\r\n  const { enqueueSnackbar } = useSnackbar();\r\n  const [fcm, setFCM] = useState(\"\");\r\n\r\n  const {\r\n    handleSubmit,\r\n    formState: { errors, isDirty, isValid, isSubmitting },\r\n    control,\r\n  } = useForm({\r\n    defaultValues: {\r\n      email: \"\",\r\n      password: \"\",\r\n      agreedToTC: false,\r\n    },\r\n    mode: \"all\",\r\n    resolver: zodResolver(loginSchema),\r\n  });\r\n\r\n  async function submitFormToLogin(formValues) {\r\n    const { agreedToTC, ...rest } = formValues;\r\n    await signInWithEmailAndPassword(auth, rest.email, rest.password)\r\n      .then(async (res) => {\r\n        const docSnapshot = await getDoc(doc(db, COLLECTIONS.USERS, res.user.uid));\r\n        const user = docSnapshot.data();\r\n\r\n        // Get the Firebase ID token for API authentication\r\n        const idToken = await res.user.getIdToken();\r\n\r\n        // HANDLE ADMIN\r\n        if (user.role === \"ADMIN\") {\r\n          navigate(\"/dashboard\");\r\n          localStorage.setItem(\"userId\", res.user.uid);\r\n          localStorage.setItem(\"access_token\", idToken);\r\n        }\r\n\r\n        // HANDLE NURSE\r\n        else if (user.role === \"NURSE\") {\r\n          localStorage.setItem(\"userId\", res.user.uid);\r\n          localStorage.setItem(\"access_token\", idToken);\r\n          await updateDoc(doc(db, COLLECTIONS.USERS, res.user.uid), { fcmToken: fcm });\r\n          navigate(\"/dashboard\");\r\n        }\r\n\r\n        // HANDLE CAREGIVER\r\n        else if (user.role === \"CAREGIVER\") {\r\n          localStorage.setItem(\"userId\", res.user.uid);\r\n          localStorage.setItem(\"access_token\", idToken);\r\n          await updateDoc(doc(db, COLLECTIONS.USERS, res.user.uid), { fcmToken: fcm });\r\n          navigate(\"/dashboard\");\r\n        }\r\n\r\n        // HANDLE CLIENT (if they can login)\r\n        else if (user.role === \"CLIENT\") {\r\n          localStorage.setItem(\"userId\", res.user.uid);\r\n          localStorage.setItem(\"access_token\", idToken);\r\n          // Clients might have different navigation logic\r\n          navigate(\"/dashboard\");\r\n        }\r\n      })\r\n      .catch((error) => {\r\n        console.error(\"SIGN IN > \", error);\r\n        // INTERNET NOT WORKING\r\n        if (error.code === \"auth/network-request-failed\") {\r\n          enqueueSnackbar(\"Make sure your internet is working\", { variant: \"error\" });\r\n        }\r\n        // USER NOT EXISTS\r\n        else if (error.code === \"auth/user-not-found\") {\r\n          enqueueSnackbar(\"Invalid email or password\", { variant: \"error\" });\r\n        }\r\n        // INVAVLID CREDENTIALS\r\n        else if (error.code === \"auth/wrong-password\" || \"auth/invalid-login-credentials\") {\r\n          enqueueSnackbar(\"Invalid email or password\", { variant: \"error\" });\r\n        } else {\r\n          enqueueSnackbar(\"Something went wrong\", { variant: \"error\" });\r\n        }\r\n      });\r\n  }\r\n\r\n  async function getPermission() {\r\n    if (await isSupported()) {\r\n      const permission = await Notification.requestPermission();\r\n      if (permission === \"granted\") {\r\n        const tokenFCM = await getToken(messaging, {\r\n          vapidKey: process.env.REACT_APP_FIREBASE_WEB_PUSH,\r\n        });\r\n        console.log(\"fcm >>\", tokenFCM);\r\n\r\n        setFCM(tokenFCM);\r\n      } else if (permission === \"denied\") {\r\n        alert(\"You've denied the notifications.\");\r\n      }\r\n    }\r\n  }\r\n\r\n  useEffect(() => {\r\n    getPermission();\r\n  }, []);\r\n\r\n  return (\r\n    <>\r\n      <Grid container justifyContent=\"center\" alignItems=\"center\" style={{ height: \"100vh\" }}>\r\n        <Grid\r\n          // md={6}\r\n          // sm={12}\r\n          size={{ xs: 12, md: 6 }}\r\n          sx={{\r\n            backgroundImage: `url(${BG_IMAGE})`,\r\n            backgroundRepeat: \"no-repeat\",\r\n            height: \"100vh\",\r\n            backgroundSize: \"cover\",\r\n            backgroundPosition: \"center\",\r\n            p: 4,\r\n            display: { xs: \"none\", sm: \"none\", md: \"flex\" },\r\n            flexDirection: \"column\",\r\n            justifyContent: \"space-between\",\r\n          }}\r\n        >\r\n          <Box display=\"flex\" flexDirection=\"column\" gap={2}>\r\n            <Box display=\"flex\" justifyContent=\"start\" alignItems=\"center\" gap={1}>\r\n              {/* <APP_LOGO fill={colors.blue} /> */}\r\n              <img src={LOGO} alt=\"insyt care logo\" style={{ objectFit: \"contain\", width: 42, height: 42 }} />\r\n              <Typography textTransform=\"uppercase\" color=\"#fff\" fontWeight={600} variant=\"h6\">\r\n                Insytcare\r\n              </Typography>\r\n            </Box>\r\n            <Typography textTransform=\"capitalize\" color=\"#fff\" fontWeight={600} variant=\"h5\">\r\n              Empowering Better Health Outcomes\r\n            </Typography>\r\n            <Typography textTransform=\"full-size-kana\" color=\"#fff\" fontWeight={400} variant=\"body1\">\r\n              We connect patients and healthcare professionals through a seamless, secure, and user-friendly platform\r\n              that makes managing care simple and effective.\r\n            </Typography>\r\n          </Box>\r\n          <img src={SIGN_IN_LEFT_CARTOON} alt=\"illustration\" style={{ objectFit: \"contain\", maxHeight: 300 }} />\r\n        </Grid>\r\n        <Grid\r\n          // md={6} sm={12}\r\n          size={{ xs: 12, md: 6 }}\r\n          sx={{\r\n            backgroundColor: \"white\",\r\n            padding: 4,\r\n            height: \"100vh\",\r\n            display: \"flex\",\r\n            justifyContent: \"center\",\r\n            alignItems: \"center\",\r\n          }}\r\n        >\r\n          <Box sx={{ maxWidth: { xs: \"100%\", lg: \"70%\" } }}>\r\n            <Typography fontWeight={500} variant=\"h4\" textAlign=\"center\" display=\"block\" mb={3}>\r\n              Sign In\r\n            </Typography>\r\n            <form onSubmit={handleSubmit(submitFormToLogin)}>\r\n              <Box display=\"flex\" flexDirection=\"column\" gap={2}>\r\n                <Box position=\"relative\">\r\n                  <Controller\r\n                    name=\"email\"\r\n                    control={control}\r\n                    render={({ field }) => {\r\n                      return <StyledField placeholder=\"Email\" type=\"email\" {...field} />;\r\n                    }}\r\n                  />\r\n                  {errors?.email?.message && (\r\n                    <Typography color=\"error\" variant=\"caption\">\r\n                      {errors?.email?.message}\r\n                    </Typography>\r\n                  )}\r\n                </Box>\r\n\r\n                {/* PASSWORD */}\r\n                <Box position=\"relative\">\r\n                  <Box position=\"relative\">\r\n                    <Controller\r\n                      name=\"password\"\r\n                      control={control}\r\n                      render={({ field }) => {\r\n                        return (\r\n                          <StyledField placeholder=\"Password\" type={showPassword ? \"text\" : \"password\"} {...field} />\r\n                        );\r\n                      }}\r\n                    />\r\n\r\n                    <IconButton\r\n                      onClick={() => setShowPassword(!showPassword)}\r\n                      sx={{ position: \"absolute\", top: 0, bottom: 0, right: 2 }}\r\n                    >\r\n                      {showPassword ? <VisibilityOff /> : <Visibility />}\r\n                    </IconButton>\r\n                  </Box>\r\n                  {errors?.password?.message && (\r\n                    <Typography color=\"error\" variant=\"caption\">\r\n                      {errors?.password?.message}\r\n                    </Typography>\r\n                  )}\r\n                </Box>\r\n              </Box>\r\n\r\n              {/* AGREED TO T&c */}\r\n              <FormControl sx={{ mt: 2 }}>\r\n                <FormControlLabel\r\n                  sx={{ fontSize: 16, \".MuiTypography-root\": { fontSize: \"12px\" } }}\r\n                  label={\r\n                    <>By selecting Continue, you agree to our Terms of Service and acknowledge our Privacy Policy.</>\r\n                  }\r\n                  control={\r\n                    <Controller\r\n                      name=\"agreedToTC\"\r\n                      control={control}\r\n                      render={({ field }) => (\r\n                        <Checkbox\r\n                          {...field}\r\n                          color=\"primary\"\r\n                          checkedIcon={<CheckBox />}\r\n                          icon={<CheckBoxOutlineBlank fill=\"#2D2D2D\" opacity={0.5} />}\r\n                          sx={{ mb: \"12px\" }}\r\n                        />\r\n                      )}\r\n                    />\r\n                  }\r\n                />\r\n              </FormControl>\r\n\r\n              {/* SUBMIT BUTTON - CREATE ACCOUNT */}\r\n              <FormControl sx={{ display: \"block\", marginTop: 2 }}>\r\n                <Button\r\n                  fullWidth\r\n                  variant=\"contained\"\r\n                  color=\"primary\"\r\n                  type=\"submit\"\r\n                  sx={{\r\n                    fontSize: 16,\r\n                    borderRadius: 2,\r\n                    py: 1.2,\r\n                    textTransform: \"none\",\r\n                    fontWeight: 400,\r\n                  }}\r\n                  disabled={isSubmitting || !isDirty || !isValid || Object.keys(errors).length > 0}\r\n                >\r\n                  {\"Sign In\"}\r\n                  {isSubmitting ? <CircularProgress size={16} color=\"inherit\" sx={{ marginLeft: 1 }} /> : null}\r\n                </Button>\r\n              </FormControl>\r\n            </form>\r\n          </Box>\r\n        </Grid>\r\n      </Grid>\r\n    </>\r\n  );\r\n};\r\n\r\nexport default Login;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SACEC,QAAQ,EACRC,WAAW,EACXC,gBAAgB,EAChBC,IAAI,EACJC,UAAU,EACVC,MAAM,EACNC,gBAAgB,EAChBC,GAAG,EACHC,UAAU,QACL,eAAe;AACtB,SAASC,UAAU,EAAEC,OAAO,QAAQ,iBAAiB;AACrD,OAAOC,CAAC,MAAM,KAAK;AACnB,SAASC,WAAW,QAAQ,yBAAyB;AACrD,SAASC,aAAa,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,oBAAoB,QAAQ,qBAAqB;AAC/F,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,0BAA0B,QAAQ,eAAe;AAC1D,SAASC,GAAG,EAAEC,IAAI,EAAEC,EAAE,EAAEC,SAAS,QAAQ,wBAAwB;AACjE,SAASC,GAAG,EAAEC,MAAM,EAAEC,SAAS,QAAQ,oBAAoB;AAC3D,SAASC,WAAW,QAAQ,cAAc;AAC1C,OAAOC,QAAQ,MAAM,8BAA8B;AACnD,OAAOC,oBAAoB,MAAM,mCAAmC;AACpE,OAAOC,IAAI,MAAM,iCAAiC;AAClD,OAAOC,MAAM,MAAM,mBAAmB;AACtC,OAAOC,KAAK,MAAM,WAAW;AAC7B,SAASC,WAAW,QAAQ,WAAW;AACvC,SAASC,YAAY,EAAEC,QAAQ,EAAEC,WAAW,QAAQ,oBAAoB;AACxE,SAASC,SAAS,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAElC,OAAO,MAAMC,WAAW,GAAG9B,CAAC,CAAC+B,MAAM,CAAC;EAClCC,KAAK,EAAEhC,CAAC,CAACiC,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,mBAAmB,CAAC,CAACF,KAAK,CAAC,uBAAuB,CAAC;EAC9EG,QAAQ,EAAEnC,CAAC,CAACiC,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,sBAAsB,CAAC,CAACE,GAAG,CAAC,CAAC,EAAE,0CAA0C,CAAC;EACxGC,UAAU,EAAErC,CAAC,CAACsC,OAAO,CAAC,CAAC,CAACC,MAAM,CAAEC,GAAG,IAAKA,GAAG,KAAK,IAAI,EAAE;IACpDC,OAAO,EAAE;EACX,CAAC;AACH,CAAC,CAAC;AAEF,MAAMC,WAAW,GAAGvB,MAAM,CAACC,KAAK,CAAC;AACjC;AACA;AACA;AACA,CAAC;AAACuB,EAAA,GAJID,WAAW;AAMjB,MAAME,KAAK,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,aAAA,EAAAC,cAAA,EAAAC,gBAAA,EAAAC,iBAAA;EAClBC,QAAQ,CAACC,KAAK,GAAG,oBAAoB;EACrC,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGjE,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAMkE,QAAQ,GAAGvC,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEwC;EAAgB,CAAC,GAAGlC,WAAW,CAAC,CAAC;EACzC,MAAM,CAACmC,GAAG,EAAEC,MAAM,CAAC,GAAGrE,QAAQ,CAAC,EAAE,CAAC;EAElC,MAAM;IACJsE,YAAY;IACZC,SAAS,EAAE;MAAEC,MAAM;MAAEC,OAAO;MAAEC,OAAO;MAAEC;IAAa,CAAC;IACrDC;EACF,CAAC,GAAGjE,OAAO,CAAC;IACVkE,aAAa,EAAE;MACbjC,KAAK,EAAE,EAAE;MACTG,QAAQ,EAAE,EAAE;MACZE,UAAU,EAAE;IACd,CAAC;IACD6B,IAAI,EAAE,KAAK;IACXC,QAAQ,EAAElE,WAAW,CAAC6B,WAAW;EACnC,CAAC,CAAC;EAEF,eAAesC,iBAAiBA,CAACC,UAAU,EAAE;IAC3C,MAAM;MAAEhC,UAAU;MAAE,GAAGiC;IAAK,CAAC,GAAGD,UAAU;IAC1C,MAAM9D,0BAA0B,CAACE,IAAI,EAAE6D,IAAI,CAACtC,KAAK,EAAEsC,IAAI,CAACnC,QAAQ,CAAC,CAC9DoC,IAAI,CAAC,MAAOC,GAAG,IAAK;MACnB,MAAMC,WAAW,GAAG,MAAM5D,MAAM,CAACD,GAAG,CAACF,EAAE,EAAEJ,WAAW,CAACoE,KAAK,EAAEF,GAAG,CAACG,IAAI,CAACC,GAAG,CAAC,CAAC;MAC1E,MAAMD,IAAI,GAAGF,WAAW,CAACI,IAAI,CAAC,CAAC;;MAE/B;MACA,MAAMC,OAAO,GAAG,MAAMN,GAAG,CAACG,IAAI,CAACI,UAAU,CAAC,CAAC;;MAE3C;MACA,IAAIJ,IAAI,CAACK,IAAI,KAAK,OAAO,EAAE;QACzB1B,QAAQ,CAAC,YAAY,CAAC;QACtB2B,YAAY,CAACC,OAAO,CAAC,QAAQ,EAAEV,GAAG,CAACG,IAAI,CAACC,GAAG,CAAC;QAC5CK,YAAY,CAACC,OAAO,CAAC,cAAc,EAAEJ,OAAO,CAAC;MAC/C;;MAEA;MAAA,KACK,IAAIH,IAAI,CAACK,IAAI,KAAK,OAAO,EAAE;QAC9BC,YAAY,CAACC,OAAO,CAAC,QAAQ,EAAEV,GAAG,CAACG,IAAI,CAACC,GAAG,CAAC;QAC5CK,YAAY,CAACC,OAAO,CAAC,cAAc,EAAEJ,OAAO,CAAC;QAC7C,MAAMhE,SAAS,CAACF,GAAG,CAACF,EAAE,EAAEJ,WAAW,CAACoE,KAAK,EAAEF,GAAG,CAACG,IAAI,CAACC,GAAG,CAAC,EAAE;UAAEO,QAAQ,EAAE3B;QAAI,CAAC,CAAC;QAC5EF,QAAQ,CAAC,YAAY,CAAC;MACxB;;MAEA;MAAA,KACK,IAAIqB,IAAI,CAACK,IAAI,KAAK,WAAW,EAAE;QAClCC,YAAY,CAACC,OAAO,CAAC,QAAQ,EAAEV,GAAG,CAACG,IAAI,CAACC,GAAG,CAAC;QAC5CK,YAAY,CAACC,OAAO,CAAC,cAAc,EAAEJ,OAAO,CAAC;QAC7C,MAAMhE,SAAS,CAACF,GAAG,CAACF,EAAE,EAAEJ,WAAW,CAACoE,KAAK,EAAEF,GAAG,CAACG,IAAI,CAACC,GAAG,CAAC,EAAE;UAAEO,QAAQ,EAAE3B;QAAI,CAAC,CAAC;QAC5EF,QAAQ,CAAC,YAAY,CAAC;MACxB;;MAEA;MAAA,KACK,IAAIqB,IAAI,CAACK,IAAI,KAAK,QAAQ,EAAE;QAC/BC,YAAY,CAACC,OAAO,CAAC,QAAQ,EAAEV,GAAG,CAACG,IAAI,CAACC,GAAG,CAAC;QAC5CK,YAAY,CAACC,OAAO,CAAC,cAAc,EAAEJ,OAAO,CAAC;QAC7C;QACAxB,QAAQ,CAAC,YAAY,CAAC;MACxB;IACF,CAAC,CAAC,CACD8B,KAAK,CAAEC,KAAK,IAAK;MAChBC,OAAO,CAACD,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;MAClC;MACA,IAAIA,KAAK,CAACE,IAAI,KAAK,6BAA6B,EAAE;QAChDhC,eAAe,CAAC,oCAAoC,EAAE;UAAEiC,OAAO,EAAE;QAAQ,CAAC,CAAC;MAC7E;MACA;MAAA,KACK,IAAIH,KAAK,CAACE,IAAI,KAAK,qBAAqB,EAAE;QAC7ChC,eAAe,CAAC,2BAA2B,EAAE;UAAEiC,OAAO,EAAE;QAAQ,CAAC,CAAC;MACpE;MACA;MAAA,KACK,IAAIH,KAAK,CAACE,IAAI,KAAK,qBAAqB,IAAI,gCAAgC,EAAE;QACjFhC,eAAe,CAAC,2BAA2B,EAAE;UAAEiC,OAAO,EAAE;QAAQ,CAAC,CAAC;MACpE,CAAC,MAAM;QACLjC,eAAe,CAAC,sBAAsB,EAAE;UAAEiC,OAAO,EAAE;QAAQ,CAAC,CAAC;MAC/D;IACF,CAAC,CAAC;EACN;EAEA,eAAeC,aAAaA,CAAA,EAAG;IAC7B,IAAI,MAAMjE,WAAW,CAAC,CAAC,EAAE;MACvB,MAAMkE,UAAU,GAAG,MAAMC,YAAY,CAACC,iBAAiB,CAAC,CAAC;MACzD,IAAIF,UAAU,KAAK,SAAS,EAAE;QAC5B,MAAMG,QAAQ,GAAG,MAAMtE,QAAQ,CAACZ,SAAS,EAAE;UACzCmF,QAAQ,EAAEC,OAAO,CAACC,GAAG,CAACC;QACxB,CAAC,CAAC;QACFX,OAAO,CAACY,GAAG,CAAC,QAAQ,EAAEL,QAAQ,CAAC;QAE/BpC,MAAM,CAACoC,QAAQ,CAAC;MAClB,CAAC,MAAM,IAAIH,UAAU,KAAK,QAAQ,EAAE;QAClCS,KAAK,CAAC,kCAAkC,CAAC;MAC3C;IACF;EACF;EAEA1E,SAAS,CAAC,MAAM;IACdgE,aAAa,CAAC,CAAC;EACjB,CAAC,EAAE,EAAE,CAAC;EAEN,oBACE9D,OAAA,CAAAE,SAAA;IAAAuE,QAAA,eACEzE,OAAA,CAACnC,IAAI;MAAC6G,SAAS;MAACC,cAAc,EAAC,QAAQ;MAACC,UAAU,EAAC,QAAQ;MAACC,KAAK,EAAE;QAAEC,MAAM,EAAE;MAAQ,CAAE;MAAAL,QAAA,gBACrFzE,OAAA,CAACnC;MACC;MACA;MAAA;QACAkH,IAAI,EAAE;UAAEC,EAAE,EAAE,EAAE;UAAEC,EAAE,EAAE;QAAE,CAAE;QACxBC,EAAE,EAAE;UACFC,eAAe,EAAE,OAAO9F,QAAQ,GAAG;UACnC+F,gBAAgB,EAAE,WAAW;UAC7BN,MAAM,EAAE,OAAO;UACfO,cAAc,EAAE,OAAO;UACvBC,kBAAkB,EAAE,QAAQ;UAC5BC,CAAC,EAAE,CAAC;UACJC,OAAO,EAAE;YAAER,EAAE,EAAE,MAAM;YAAES,EAAE,EAAE,MAAM;YAAER,EAAE,EAAE;UAAO,CAAC;UAC/CS,aAAa,EAAE,QAAQ;UACvBf,cAAc,EAAE;QAClB,CAAE;QAAAF,QAAA,gBAEFzE,OAAA,CAAC/B,GAAG;UAACuH,OAAO,EAAC,MAAM;UAACE,aAAa,EAAC,QAAQ;UAACC,GAAG,EAAE,CAAE;UAAAlB,QAAA,gBAChDzE,OAAA,CAAC/B,GAAG;YAACuH,OAAO,EAAC,MAAM;YAACb,cAAc,EAAC,OAAO;YAACC,UAAU,EAAC,QAAQ;YAACe,GAAG,EAAE,CAAE;YAAAlB,QAAA,gBAEpEzE,OAAA;cAAK4F,GAAG,EAAErG,IAAK;cAACsG,GAAG,EAAC,iBAAiB;cAAChB,KAAK,EAAE;gBAAEiB,SAAS,EAAE,SAAS;gBAAEC,KAAK,EAAE,EAAE;gBAAEjB,MAAM,EAAE;cAAG;YAAE;cAAAkB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAChGnG,OAAA,CAAC9B,UAAU;cAACkI,aAAa,EAAC,WAAW;cAACC,KAAK,EAAC,MAAM;cAACC,UAAU,EAAE,GAAI;cAACzC,OAAO,EAAC,IAAI;cAAAY,QAAA,EAAC;YAEjF;cAAAuB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eACNnG,OAAA,CAAC9B,UAAU;YAACkI,aAAa,EAAC,YAAY;YAACC,KAAK,EAAC,MAAM;YAACC,UAAU,EAAE,GAAI;YAACzC,OAAO,EAAC,IAAI;YAAAY,QAAA,EAAC;UAElF;YAAAuB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbnG,OAAA,CAAC9B,UAAU;YAACkI,aAAa,EAAC,gBAAgB;YAACC,KAAK,EAAC,MAAM;YAACC,UAAU,EAAE,GAAI;YAACzC,OAAO,EAAC,OAAO;YAAAY,QAAA,EAAC;UAGzF;YAAAuB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eACNnG,OAAA;UAAK4F,GAAG,EAAEtG,oBAAqB;UAACuG,GAAG,EAAC,cAAc;UAAChB,KAAK,EAAE;YAAEiB,SAAS,EAAE,SAAS;YAAES,SAAS,EAAE;UAAI;QAAE;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClG,CAAC,eACPnG,OAAA,CAACnC;MACC;MAAA;QACAkH,IAAI,EAAE;UAAEC,EAAE,EAAE,EAAE;UAAEC,EAAE,EAAE;QAAE,CAAE;QACxBC,EAAE,EAAE;UACFsB,eAAe,EAAE,OAAO;UACxBC,OAAO,EAAE,CAAC;UACV3B,MAAM,EAAE,OAAO;UACfU,OAAO,EAAE,MAAM;UACfb,cAAc,EAAE,QAAQ;UACxBC,UAAU,EAAE;QACd,CAAE;QAAAH,QAAA,eAEFzE,OAAA,CAAC/B,GAAG;UAACiH,EAAE,EAAE;YAAEwB,QAAQ,EAAE;cAAE1B,EAAE,EAAE,MAAM;cAAE2B,EAAE,EAAE;YAAM;UAAE,CAAE;UAAAlC,QAAA,gBAC/CzE,OAAA,CAAC9B,UAAU;YAACoI,UAAU,EAAE,GAAI;YAACzC,OAAO,EAAC,IAAI;YAAC+C,SAAS,EAAC,QAAQ;YAACpB,OAAO,EAAC,OAAO;YAACqB,EAAE,EAAE,CAAE;YAAApC,QAAA,EAAC;UAEpF;YAAAuB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbnG,OAAA;YAAM8G,QAAQ,EAAE/E,YAAY,CAACU,iBAAiB,CAAE;YAAAgC,QAAA,gBAC9CzE,OAAA,CAAC/B,GAAG;cAACuH,OAAO,EAAC,MAAM;cAACE,aAAa,EAAC,QAAQ;cAACC,GAAG,EAAE,CAAE;cAAAlB,QAAA,gBAChDzE,OAAA,CAAC/B,GAAG;gBAAC8I,QAAQ,EAAC,UAAU;gBAAAtC,QAAA,gBACtBzE,OAAA,CAAC7B,UAAU;kBACT6I,IAAI,EAAC,OAAO;kBACZ3E,OAAO,EAAEA,OAAQ;kBACjB4E,MAAM,EAAEA,CAAC;oBAAEC;kBAAM,CAAC,KAAK;oBACrB,oBAAOlH,OAAA,CAACe,WAAW;sBAACoG,WAAW,EAAC,OAAO;sBAACC,IAAI,EAAC,OAAO;sBAAA,GAAKF;oBAAK;sBAAAlB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC;kBACpE;gBAAE;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,EACD,CAAAlE,MAAM,aAANA,MAAM,wBAAAd,aAAA,GAANc,MAAM,CAAE5B,KAAK,cAAAc,aAAA,uBAAbA,aAAA,CAAeL,OAAO,kBACrBd,OAAA,CAAC9B,UAAU;kBAACmI,KAAK,EAAC,OAAO;kBAACxC,OAAO,EAAC,SAAS;kBAAAY,QAAA,EACxCxC,MAAM,aAANA,MAAM,wBAAAb,cAAA,GAANa,MAAM,CAAE5B,KAAK,cAAAe,cAAA,uBAAbA,cAAA,CAAeN;gBAAO;kBAAAkF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACb,CACb;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eAGNnG,OAAA,CAAC/B,GAAG;gBAAC8I,QAAQ,EAAC,UAAU;gBAAAtC,QAAA,gBACtBzE,OAAA,CAAC/B,GAAG;kBAAC8I,QAAQ,EAAC,UAAU;kBAAAtC,QAAA,gBACtBzE,OAAA,CAAC7B,UAAU;oBACT6I,IAAI,EAAC,UAAU;oBACf3E,OAAO,EAAEA,OAAQ;oBACjB4E,MAAM,EAAEA,CAAC;sBAAEC;oBAAM,CAAC,KAAK;sBACrB,oBACElH,OAAA,CAACe,WAAW;wBAACoG,WAAW,EAAC,UAAU;wBAACC,IAAI,EAAE3F,YAAY,GAAG,MAAM,GAAG,UAAW;wBAAA,GAAKyF;sBAAK;wBAAAlB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG,CAAC;oBAE/F;kBAAE;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAEFnG,OAAA,CAAClC,UAAU;oBACTuJ,OAAO,EAAEA,CAAA,KAAM3F,eAAe,CAAC,CAACD,YAAY,CAAE;oBAC9CyD,EAAE,EAAE;sBAAE6B,QAAQ,EAAE,UAAU;sBAAEO,GAAG,EAAE,CAAC;sBAAEC,MAAM,EAAE,CAAC;sBAAEC,KAAK,EAAE;oBAAE,CAAE;oBAAA/C,QAAA,EAEzDhD,YAAY,gBAAGzB,OAAA,CAACzB,aAAa;sBAAAyH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,gBAAGnG,OAAA,CAACxB,UAAU;sBAAAwH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC,EACL,CAAAlE,MAAM,aAANA,MAAM,wBAAAZ,gBAAA,GAANY,MAAM,CAAEzB,QAAQ,cAAAa,gBAAA,uBAAhBA,gBAAA,CAAkBP,OAAO,kBACxBd,OAAA,CAAC9B,UAAU;kBAACmI,KAAK,EAAC,OAAO;kBAACxC,OAAO,EAAC,SAAS;kBAAAY,QAAA,EACxCxC,MAAM,aAANA,MAAM,wBAAAX,iBAAA,GAANW,MAAM,CAAEzB,QAAQ,cAAAc,iBAAA,uBAAhBA,iBAAA,CAAkBR;gBAAO;kBAAAkF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChB,CACb;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGNnG,OAAA,CAACrC,WAAW;cAACuH,EAAE,EAAE;gBAAEuC,EAAE,EAAE;cAAE,CAAE;cAAAhD,QAAA,eACzBzE,OAAA,CAACpC,gBAAgB;gBACfsH,EAAE,EAAE;kBAAEwC,QAAQ,EAAE,EAAE;kBAAE,qBAAqB,EAAE;oBAAEA,QAAQ,EAAE;kBAAO;gBAAE,CAAE;gBAClEC,KAAK,eACH3H,OAAA,CAAAE,SAAA;kBAAAuE,QAAA,EAAE;gBAA4F,gBAAE,CACjG;gBACDpC,OAAO,eACLrC,OAAA,CAAC7B,UAAU;kBACT6I,IAAI,EAAC,YAAY;kBACjB3E,OAAO,EAAEA,OAAQ;kBACjB4E,MAAM,EAAEA,CAAC;oBAAEC;kBAAM,CAAC,kBAChBlH,OAAA,CAACtC,QAAQ;oBAAA,GACHwJ,KAAK;oBACTb,KAAK,EAAC,SAAS;oBACfuB,WAAW,eAAE5H,OAAA,CAACvB,QAAQ;sBAAAuH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAE;oBAC1B0B,IAAI,eAAE7H,OAAA,CAACtB,oBAAoB;sBAACoJ,IAAI,EAAC,SAAS;sBAACC,OAAO,EAAE;oBAAI;sBAAA/B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAE;oBAC5DjB,EAAE,EAAE;sBAAE2B,EAAE,EAAE;oBAAO;kBAAE;oBAAAb,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpB;gBACD;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cACF;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACS,CAAC,eAGdnG,OAAA,CAACrC,WAAW;cAACuH,EAAE,EAAE;gBAAEM,OAAO,EAAE,OAAO;gBAAEwC,SAAS,EAAE;cAAE,CAAE;cAAAvD,QAAA,eAClDzE,OAAA,CAACjC,MAAM;gBACLkK,SAAS;gBACTpE,OAAO,EAAC,WAAW;gBACnBwC,KAAK,EAAC,SAAS;gBACfe,IAAI,EAAC,QAAQ;gBACblC,EAAE,EAAE;kBACFwC,QAAQ,EAAE,EAAE;kBACZQ,YAAY,EAAE,CAAC;kBACfC,EAAE,EAAE,GAAG;kBACP/B,aAAa,EAAE,MAAM;kBACrBE,UAAU,EAAE;gBACd,CAAE;gBACF8B,QAAQ,EAAEhG,YAAY,IAAI,CAACF,OAAO,IAAI,CAACC,OAAO,IAAIkG,MAAM,CAACC,IAAI,CAACrG,MAAM,CAAC,CAACsG,MAAM,GAAG,CAAE;gBAAA9D,QAAA,GAEhF,SAAS,EACTrC,YAAY,gBAAGpC,OAAA,CAAChC,gBAAgB;kBAAC+G,IAAI,EAAE,EAAG;kBAACsB,KAAK,EAAC,SAAS;kBAACnB,EAAE,EAAE;oBAAEsD,UAAU,EAAE;kBAAE;gBAAE;kBAAAxC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,GAAG,IAAI;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC,gBACP,CAAC;AAEP,CAAC;AAACjF,EAAA,CA1PID,KAAK;EAAA,QAGQ7B,WAAW,EACAM,WAAW,EAOnCtB,OAAO;AAAA;AAAAqK,GAAA,GAXPxH,KAAK;AA4PX,eAAeA,KAAK;AAAC,IAAAD,EAAA,EAAAyH,GAAA;AAAAC,YAAA,CAAA1H,EAAA;AAAA0H,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}