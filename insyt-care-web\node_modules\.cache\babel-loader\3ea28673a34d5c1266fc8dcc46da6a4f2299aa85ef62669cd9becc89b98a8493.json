{"ast": null, "code": "import axios from \"axios\";\nimport { auth } from \"./firebase.config\";\nconst AppInstance = axios.create({\n  baseURL: \"https://us-central1-insyt-care.cloudfunctions.net\"\n});\nAppInstance.interceptors.request.use(async request => {\n  try {\n    // Get current user from Firebase Auth\n    const currentUser = auth.currentUser;\n    if (currentUser) {\n      // Get fresh ID token (this will automatically refresh if needed)\n      const idToken = await currentUser.getIdToken(true);\n      request.headers.Authorization = `Bearer ${idToken}`;\n      // Update localStorage with fresh token\n      localStorage.setItem(\"access_token\", idToken);\n    } else {\n      // Fallback to stored token if no current user\n      const accessToken = localStorage.getItem(\"access_token\");\n      if (accessToken) {\n        request.headers.Authorization = `Bearer ${accessToken}`;\n      }\n    }\n  } catch (error) {\n    console.error(\"Error getting ID token:\", error);\n    // Fallback to stored token\n    const accessToken = localStorage.getItem(\"access_token\");\n    if (accessToken) {\n      request.headers.Authorization = `Bearer ${accessToken}`;\n    }\n  }\n  return request;\n});\nAppInstance.interceptors.response.use(response => response, error => {\n  var _error$response;\n  // Handle 401 errors by clearing stored tokens\n  if (((_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.status) === 401) {\n    localStorage.removeItem(\"access_token\");\n    localStorage.removeItem(\"userId\");\n    // Optionally redirect to login page\n    // window.location.href = '/login';\n  }\n  return Promise.reject(error);\n});\nexport default AppInstance;", "map": {"version": 3, "names": ["axios", "auth", "AppInstance", "create", "baseURL", "interceptors", "request", "use", "currentUser", "idToken", "getIdToken", "headers", "Authorization", "localStorage", "setItem", "accessToken", "getItem", "error", "console", "response", "_error$response", "status", "removeItem", "Promise", "reject"], "sources": ["D:/Softwares/insyt-care/insyt-care-web/src/config/axios.config.js"], "sourcesContent": ["import axios from \"axios\";\r\nimport { auth } from \"./firebase.config\";\r\n\r\nconst AppInstance = axios.create({\r\n  baseURL: \"https://us-central1-insyt-care.cloudfunctions.net\",\r\n});\r\n\r\nAppInstance.interceptors.request.use(async (request) => {\r\n  try {\r\n    // Get current user from Firebase Auth\r\n    const currentUser = auth.currentUser;\r\n    if (currentUser) {\r\n      // Get fresh ID token (this will automatically refresh if needed)\r\n      const idToken = await currentUser.getIdToken(true);\r\n      request.headers.Authorization = `Bearer ${idToken}`;\r\n      // Update localStorage with fresh token\r\n      localStorage.setItem(\"access_token\", idToken);\r\n    } else {\r\n      // Fallback to stored token if no current user\r\n      const accessToken = localStorage.getItem(\"access_token\");\r\n      if (accessToken) {\r\n        request.headers.Authorization = `Bearer ${accessToken}`;\r\n      }\r\n    }\r\n  } catch (error) {\r\n    console.error(\"Error getting ID token:\", error);\r\n    // Fallback to stored token\r\n    const accessToken = localStorage.getItem(\"access_token\");\r\n    if (accessToken) {\r\n      request.headers.Authorization = `Bearer ${accessToken}`;\r\n    }\r\n  }\r\n  return request;\r\n});\r\n\r\nAppInstance.interceptors.response.use(\r\n  (response) => response,\r\n  (error) => {\r\n    // Handle 401 errors by clearing stored tokens\r\n    if (error.response?.status === 401) {\r\n      localStorage.removeItem(\"access_token\");\r\n      localStorage.removeItem(\"userId\");\r\n      // Optionally redirect to login page\r\n      // window.location.href = '/login';\r\n    }\r\n    return Promise.reject(error);\r\n  }\r\n);\r\n\r\nexport default AppInstance;\r\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,QAAQ,mBAAmB;AAExC,MAAMC,WAAW,GAAGF,KAAK,CAACG,MAAM,CAAC;EAC/BC,OAAO,EAAE;AACX,CAAC,CAAC;AAEFF,WAAW,CAACG,YAAY,CAACC,OAAO,CAACC,GAAG,CAAC,MAAOD,OAAO,IAAK;EACtD,IAAI;IACF;IACA,MAAME,WAAW,GAAGP,IAAI,CAACO,WAAW;IACpC,IAAIA,WAAW,EAAE;MACf;MACA,MAAMC,OAAO,GAAG,MAAMD,WAAW,CAACE,UAAU,CAAC,IAAI,CAAC;MAClDJ,OAAO,CAACK,OAAO,CAACC,aAAa,GAAG,UAAUH,OAAO,EAAE;MACnD;MACAI,YAAY,CAACC,OAAO,CAAC,cAAc,EAAEL,OAAO,CAAC;IAC/C,CAAC,MAAM;MACL;MACA,MAAMM,WAAW,GAAGF,YAAY,CAACG,OAAO,CAAC,cAAc,CAAC;MACxD,IAAID,WAAW,EAAE;QACfT,OAAO,CAACK,OAAO,CAACC,aAAa,GAAG,UAAUG,WAAW,EAAE;MACzD;IACF;EACF,CAAC,CAAC,OAAOE,KAAK,EAAE;IACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;IAC/C;IACA,MAAMF,WAAW,GAAGF,YAAY,CAACG,OAAO,CAAC,cAAc,CAAC;IACxD,IAAID,WAAW,EAAE;MACfT,OAAO,CAACK,OAAO,CAACC,aAAa,GAAG,UAAUG,WAAW,EAAE;IACzD;EACF;EACA,OAAOT,OAAO;AAChB,CAAC,CAAC;AAEFJ,WAAW,CAACG,YAAY,CAACc,QAAQ,CAACZ,GAAG,CAClCY,QAAQ,IAAKA,QAAQ,EACrBF,KAAK,IAAK;EAAA,IAAAG,eAAA;EACT;EACA,IAAI,EAAAA,eAAA,GAAAH,KAAK,CAACE,QAAQ,cAAAC,eAAA,uBAAdA,eAAA,CAAgBC,MAAM,MAAK,GAAG,EAAE;IAClCR,YAAY,CAACS,UAAU,CAAC,cAAc,CAAC;IACvCT,YAAY,CAACS,UAAU,CAAC,QAAQ,CAAC;IACjC;IACA;EACF;EACA,OAAOC,OAAO,CAACC,MAAM,CAACP,KAAK,CAAC;AAC9B,CACF,CAAC;AAED,eAAef,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}